<!--knowledge.wxml-->
<navigation-bar title="育儿知识" back="{{true}}" color="white" background="#4A90E2"></navigation-bar>
<scroll-view class="scrollarea" scroll-y type="list">
  <view class="container">
    <!-- 搜索栏 -->
    <view class="search-bar">
      <view class="search-input-wrapper">
        <image class="search-icon" src="/images/search-icon.png" mode="aspectFit"></image>
        <input class="search-input" placeholder="搜索育儿知识" value="{{searchKeyword}}" bindinput="onSearchInput" bindconfirm="onSearch" />
      </view>
      <view class="search-btn" bindtap="onSearch">搜索</view>
    </view>
    
    <!-- 分类标签 -->
    <view class="category-tabs">
      <scroll-view class="tabs-scroll" scroll-x>
        <view class="tab-item {{currentCategory === item.id ? 'active' : ''}}" 
              wx:for="{{categories}}" 
              wx:key="id" 
              bindtap="onCategoryChange" 
              data-id="{{item.id}}">
          {{item.name}}
        </view>
      </scroll-view>
    </view>
    
    <!-- 知识列表 -->
    <view class="knowledge-list">
      <view class="knowledge-item" 
            wx:for="{{knowledgeList}}" 
            wx:key="id" 
            bindtap="goToDetail" 
            data-id="{{item.id}}">
        <image class="knowledge-thumb" src="{{item.thumbnail}}" mode="aspectFill"></image>
        <view class="knowledge-content">
          <view class="knowledge-header">
            <text class="knowledge-title">{{item.title}}</text>
            <view class="knowledge-tags">
              <text class="tag category-tag">{{item.category}}</text>
              <text class="tag age-tag" wx:if="{{item.ageGroup}}">{{item.ageGroup}}</text>
            </view>
          </view>
          <text class="knowledge-summary">{{item.summary}}</text>
          <view class="knowledge-meta">
            <view class="meta-left">
              <text class="author">{{item.author}}</text>
              <text class="publish-time">{{item.publishTime}}</text>
            </view>
            <view class="meta-right">
              <view class="stat-item">
                <image class="stat-icon" src="/images/view-icon.png" mode="aspectFit"></image>
                <text class="stat-text">{{item.viewCount}}</text>
              </view>
              <view class="stat-item">
                <image class="stat-icon" src="/images/like-icon.png" mode="aspectFit"></image>
                <text class="stat-text">{{item.likeCount}}</text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 加载更多 -->
    <view class="load-more" wx:if="{{hasMore}}">
      <view class="loading" wx:if="{{loading}}">
        <text>加载中...</text>
      </view>
      <view class="load-more-btn" wx:else bindtap="loadMore">
        <text>加载更多</text>
      </view>
    </view>
    
    <!-- 空状态 -->
    <view class="empty-state" wx:if="{{knowledgeList.length === 0 && !loading}}">
      <image class="empty-icon" src="/images/empty-icon.png" mode="aspectFit"></image>
      <text class="empty-text">暂无相关知识内容</text>
      <text class="empty-tip">试试搜索其他关键词</text>
    </view>
  </view>
</scroll-view>

<!-- 浮动操作按钮 -->
<view class="fab-container">
  <view class="fab-btn" bindtap="goToFavorites">
    <image class="fab-icon" src="/images/favorite-icon.png" mode="aspectFit"></image>
  </view>
</view>
