{"pages": ["pages/index/index", "pages/calculator/calculator", "pages/knowledge/knowledge", "pages/knowledge-detail/knowledge-detail", "pages/policy-news/policy-news", "pages/news-detail/news-detail", "pages/profile/profile", "pages/logs/logs"], "window": {"navigationBarTextStyle": "black", "navigationStyle": "custom"}, "style": "v2", "rendererOptions": {"skyline": {"defaultDisplayBlock": true, "defaultContentBox": true, "disableABTest": true, "sdkVersionBegin": "3.0.0", "sdkVersionEnd": "15.255.255"}}, "componentFramework": "glass-easel", "sitemapLocation": "sitemap.json", "lazyCodeLoading": "requiredComponents"}