<!--calculator.wxml-->
<navigation-bar title="补贴计算" back="{{true}}" color="white" background="#4A90E2"></navigation-bar>
<scroll-view class="scrollarea" scroll-y type="list">
  <view class="container">
    <!-- 计算器表单 -->
    <view class="calculator-form">
      <view class="form-section">
        <view class="section-title">基本信息</view>
        
        <!-- 所在地区 -->
        <view class="form-item">
          <text class="label">所在地区</text>
          <picker mode="multiSelector" range="{{regionData}}" value="{{regionIndex}}" bindchange="onRegionChange">
            <view class="picker-value">
              {{selectedRegion || '请选择地区'}}
            </view>
          </picker>
        </view>
        
        <!-- 孩子数量 -->
        <view class="form-item">
          <text class="label">孩子数量</text>
          <picker mode="selector" range="{{childCountOptions}}" value="{{childCountIndex}}" bindchange="onChildCountChange">
            <view class="picker-value">
              {{childCountOptions[childCountIndex] || '请选择'}}
            </view>
          </picker>
        </view>
        
        <!-- 孩子年龄 -->
        <view class="form-item" wx:if="{{childCountIndex > 0}}">
          <text class="label">孩子年龄</text>
          <view class="age-inputs">
            <view class="age-item" wx:for="{{childAges}}" wx:key="index">
              <text class="age-label">第{{index + 1}}个孩子</text>
              <picker mode="selector" range="{{ageOptions}}" value="{{item}}" bindchange="onChildAgeChange" data-index="{{index}}">
                <view class="picker-value small">
                  {{ageOptions[item] || '请选择年龄'}}
                </view>
              </picker>
            </view>
          </view>
        </view>
      </view>
      
      <view class="form-section">
        <view class="section-title">收入信息</view>
        
        <!-- 家庭月收入 -->
        <view class="form-item">
          <text class="label">家庭月收入</text>
          <input class="input" type="number" placeholder="请输入家庭月收入" value="{{monthlyIncome}}" bindinput="onIncomeInput" />
          <text class="unit">元</text>
        </view>
        
        <!-- 就业状态 -->
        <view class="form-item">
          <text class="label">就业状态</text>
          <picker mode="selector" range="{{employmentOptions}}" value="{{employmentIndex}}" bindchange="onEmploymentChange">
            <view class="picker-value">
              {{employmentOptions[employmentIndex] || '请选择'}}
            </view>
          </picker>
        </view>
        
        <!-- 社保缴费 -->
        <view class="form-item">
          <text class="label">社保缴费情况</text>
          <picker mode="selector" range="{{socialSecurityOptions}}" value="{{socialSecurityIndex}}" bindchange="onSocialSecurityChange">
            <view class="picker-value">
              {{socialSecurityOptions[socialSecurityIndex] || '请选择'}}
            </view>
          </picker>
        </view>
      </view>
      
      <!-- 计算按钮 -->
      <view class="calculate-btn" bindtap="calculateSubsidy">
        开始计算
      </view>
    </view>
    
    <!-- 计算结果 -->
    <view class="result-section" wx:if="{{showResult}}">
      <view class="result-header">
        <text class="result-title">计算结果</text>
        <text class="result-subtitle">根据您的情况，可申请以下补贴</text>
      </view>
      
      <view class="result-list">
        <view class="result-item" wx:for="{{subsidyResults}}" wx:key="type">
          <view class="result-info">
            <text class="subsidy-name">{{item.name}}</text>
            <text class="subsidy-desc">{{item.description}}</text>
          </view>
          <view class="result-amount">
            <text class="amount">{{item.amount}}</text>
            <text class="unit">元/月</text>
          </view>
        </view>
      </view>
      
      <view class="total-amount">
        <text class="total-label">预计每月总补贴</text>
        <text class="total-value">{{totalSubsidy}}</text>
        <text class="total-unit">元</text>
      </view>
      
      <!-- 申请指南 -->
      <view class="apply-guide">
        <text class="guide-title">申请指南</text>
        <view class="guide-steps">
          <view class="step-item" wx:for="{{applySteps}}" wx:key="index">
            <view class="step-number">{{index + 1}}</view>
            <text class="step-text">{{item}}</text>
          </view>
        </view>
      </view>
      
      <!-- 操作按钮 -->
      <view class="action-buttons">
        <button class="btn-secondary" bindtap="saveResult">保存结果</button>
        <button class="btn-primary" bindtap="shareResult">分享结果</button>
      </view>
    </view>
  </view>
</scroll-view>
